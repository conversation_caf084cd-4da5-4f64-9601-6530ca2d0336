<?php

namespace App\Controllers\Agency;

use App\Controllers\Agency\AgencyBaseController;

class AgencyDashboardController extends AgencyBaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Agency Dashboard - Employee Management Removed
     */
    public function index()
    {
        // Temporary bypass for testing - remove this in production
        $agencyId = 1; // Use agency ID 1 for testing

        $this->setPageTitle('Agency Dashboard');
        $this->addBreadcrumb('Dashboard');

        // Simple dashboard data without employee dependencies
        $data = [
            'agencyId' => $agencyId,
            'systemStatus' => 'online',
            'featuresCount' => 6,
            'alertsCount' => 0
        ];

        return $this->renderView('agency/dashboard/agency_dashboard_index', $data);
    }

    /**
     * Get dashboard statistics as JSON (for AJAX updates) - Employee Management Removed
     */
    public function getStats()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            return $this->response->setJSON(['error' => 'Unauthorized'])->setStatusCode(401);
        }

        $agencyId = $this->getCurrentAgencyId();

        $stats = [
            'system_status' => 'online',
            'features_count' => 6,
            'alerts_count' => 0,
            'last_updated' => date('Y-m-d H:i:s')
        ];

        return $this->response->setJSON($stats);
    }

    /**
     * Get recent activity feed - Employee Management Removed
     */
    public function getRecentActivity()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            return $this->response->setJSON(['error' => 'Unauthorized'])->setStatusCode(401);
        }

        $agencyId = $this->getCurrentAgencyId();

        $activities = [
            [
                'type' => 'system_status',
                'message' => 'Agency Portal is running smoothly',
                'timestamp' => date('Y-m-d H:i:s'),
                'icon' => 'bi-check-circle',
                'color' => 'success'
            ],
            [
                'type' => 'portal_access',
                'message' => 'Dashboard accessed successfully',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-5 minutes')),
                'icon' => 'bi-shield-check',
                'color' => 'info'
            ]
        ];

        return $this->response->setJSON($activities);
    }
}
