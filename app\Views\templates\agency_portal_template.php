<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' : '' ?>Agency Portal - CHS PNG</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= base_url('favicon.ico') ?>">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --chs-blue: #1A4E8C;
            --chs-red: #C8102E;
            --agency-accent: #20B2AA;
            --light-gray: #F8F9FA;
            --dark-gray: #343A40;
        }
        
        .navbar-brand {
            font-weight: 600;
        }
        
        .agency-header {
            background: linear-gradient(135deg, var(--chs-blue) 0%, var(--agency-accent) 100%);
            color: white;
            padding: 1rem 0;
        }
        
        .nav-pills .nav-link {
            color: var(--dark-gray);
            border-radius: 0.5rem;
            margin: 0 0.25rem;
        }
        
        .nav-pills .nav-link.active {
            background-color: var(--chs-blue);
        }
        
        .nav-pills .nav-link:hover {
            background-color: var(--light-gray);
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.75rem;
        }
        
        .card-header {
            background-color: var(--light-gray);
            border-bottom: 1px solid #dee2e6;
            border-radius: 0.75rem 0.75rem 0 0 !important;
        }
        
        .btn-primary {
            background-color: var(--chs-blue);
            border-color: var(--chs-blue);
        }
        
        .btn-primary:hover {
            background-color: #164080;
            border-color: #164080;
        }
        
        .btn-accent {
            background-color: var(--agency-accent);
            border-color: var(--agency-accent);
            color: white;
        }
        
        .btn-accent:hover {
            background-color: #1a9b96;
            border-color: #1a9b96;
            color: white;
        }
        
        .text-accent {
            color: var(--agency-accent) !important;
        }
        
        .bg-accent {
            background-color: var(--agency-accent) !important;
        }
        
        .alert {
            border-radius: 0.75rem;
        }
        
        .breadcrumb {
            background-color: transparent;
            padding: 0;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: ">";
        }
        
        .stats-card {
            transition: transform 0.2s;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
        }
        
        .sidebar {
            min-height: calc(100vh - 200px);
        }
        
        .main-content {
            min-height: calc(100vh - 200px);
        }
        
        .footer {
            background-color: var(--dark-gray);
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Agency Header -->
    <header class="agency-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8 d-flex align-items-center">
                    <img src="<?= base_url('assets/images/chs-logo.png') ?>" alt="CHS Logo"
                         style="width: 50px; height: 50px; margin-right: 1rem;">
                    <div>
                        <h4 class="mb-0">
                            <?= isset($currentAgency) ? esc($currentAgency['name']) : 'Agency Portal' ?>
                        </h4>
                        <small class="text-light">
                            <?= isset($currentAgency) ? 'Code: ' . esc($currentAgency['agency_code']) : 'Christian Health Services PNG' ?>
                        </small>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <?= isset($currentUser) ? esc($currentUser['first_name'] . ' ' . $currentUser['last_name']) : 'User' ?>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/agency/profile"><i class="bi bi-person"></i> Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="/admin/logout" class="d-inline">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right"></i> Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile App Style - No Traditional Navigation -->

    <!-- Flash Messages -->
    <?php if (isset($flashMessages) && is_array($flashMessages)): ?>
        <div class="container-fluid mt-3">
            <?php foreach ($flashMessages as $type => $message): ?>
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?= $type === 'error' ? 'danger' : $type ?> alert-dismissible fade show" role="alert">
                        <?= esc($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <!-- Breadcrumbs -->
    <?php if (isset($breadcrumbs) && !empty($breadcrumbs)): ?>
        <div class="container-fluid mt-3">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                        <?php if ($index === count($breadcrumbs) - 1): ?>
                            <li class="breadcrumb-item active" aria-current="page"><?= esc($breadcrumb['title']) ?></li>
                        <?php else: ?>
                            <li class="breadcrumb-item">
                                <?php if (!empty($breadcrumb['url'])): ?>
                                    <a href="<?= esc($breadcrumb['url']) ?>"><?= esc($breadcrumb['title']) ?></a>
                                <?php else: ?>
                                    <?= esc($breadcrumb['title']) ?>
                                <?php endif; ?>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ol>
            </nav>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="container-fluid mt-4 main-content">
        <?= $this->renderSection('content') ?>
    </main>

    <!-- Footer -->
    <footer class="footer mt-5">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <h6>CHS PNG - Agency Portal</h6>
                    <p class="mb-0 small">Employee Management System</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0 small">
                        &copy; <?= date('Y') ?> Church Health Services PNG. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
    
    <?= $this->renderSection('scripts') ?>
</body>
</html>
