<?php

namespace App\Controllers\Agency;

use App\Models\EmployeeModel;
use App\Models\EmployeeDocumentModel;
use App\Models\EmployeeBankingModel;
use App\Models\EmployeeQualificationModel;

class AgencyReportsController extends AgencyBaseController
{
    protected $employeeModel;
    protected $employeeDocumentModel;
    protected $employeeBankingModel;
    protected $employeeQualificationModel;

    public function __construct()
    {
        parent::__construct();
        $this->employeeModel = new EmployeeModel();
        $this->employeeDocumentModel = new EmployeeDocumentModel();
        $this->employeeBankingModel = new EmployeeBankingModel();
        $this->employeeQualificationModel = new EmployeeQualificationModel();
    }

    /**
     * Display reports dashboard
     */
    public function index()
    {
        $data = [
            'pageTitle' => 'Reports Dashboard',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => '/agency/dashboard'],
                ['title' => 'Reports', 'url' => '']
            ]
        ];

        // Get summary statistics
        $data['employeeStats'] = $this->getEmployeeStats();
        $data['documentStats'] = $this->getDocumentStats();
        $data['bankingStats'] = $this->getBankingStats();
        $data['qualificationStats'] = $this->getQualificationStats();

        return view('agency/reports/reports_index', $data);
    }

    /**
     * Employee reports
     */
    public function employees()
    {
        $data = [
            'pageTitle' => 'Employee Reports',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => '/agency/dashboard'],
                ['title' => 'Reports', 'url' => '/agency/reports'],
                ['title' => 'Employees', 'url' => '']
            ]
        ];

        // Get filters from request
        $filters = [
            'status' => $this->request->getGet('status'),
            'department' => $this->request->getGet('department'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to')
        ];

        $data['employees'] = $this->employeeModel->getAgencyEmployeesReport($this->agencyId, $filters);
        $data['filters'] = $filters;
        $data['departments'] = $this->employeeModel->getAgencyDepartments($this->agencyId);

        return view('agency/reports/employees_report', $data);
    }

    /**
     * Onboarding completion reports
     */
    public function onboarding()
    {
        $data = [
            'pageTitle' => 'Onboarding Reports',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => '/agency/dashboard'],
                ['title' => 'Reports', 'url' => '/agency/reports'],
                ['title' => 'Onboarding', 'url' => '']
            ]
        ];

        $data['onboardingData'] = $this->getOnboardingCompletionData();

        return view('agency/reports/onboarding_report', $data);
    }

    /**
     * Document reports
     */
    public function documents()
    {
        $data = [
            'pageTitle' => 'Document Reports',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => '/agency/dashboard'],
                ['title' => 'Reports', 'url' => '/agency/reports'],
                ['title' => 'Documents', 'url' => '']
            ]
        ];

        // Get filters from request
        $filters = [
            'status' => $this->request->getGet('status'),
            'document_type' => $this->request->getGet('document_type'),
            'date_from' => $this->request->getGet('date_from'),
            'date_to' => $this->request->getGet('date_to')
        ];

        $data['documents'] = $this->employeeDocumentModel->getAgencyDocumentsReport($this->agencyId, $filters);
        $data['filters'] = $filters;
        $data['documentTypes'] = $this->getDocumentTypes();

        return view('agency/reports/documents_report', $data);
    }

    /**
     * Banking reports
     */
    public function banking()
    {
        $data = [
            'pageTitle' => 'Banking Reports',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => '/agency/dashboard'],
                ['title' => 'Reports', 'url' => '/agency/reports'],
                ['title' => 'Banking', 'url' => '']
            ]
        ];

        $data['bankingData'] = $this->getBankingCompletionData();

        return view('agency/reports/banking_report', $data);
    }

    /**
     * Get employee statistics
     */
    private function getEmployeeStats()
    {
        return [
            'total' => $this->employeeModel->countAgencyEmployees($this->agencyId),
            'active' => $this->employeeModel->countAgencyEmployeesByStatus($this->agencyId, 'active'),
            'pending' => $this->employeeModel->countAgencyEmployeesByStatus($this->agencyId, 'pending'),
            'inactive' => $this->employeeModel->countAgencyEmployeesByStatus($this->agencyId, 'inactive'),
            'terminated' => $this->employeeModel->countAgencyEmployeesByStatus($this->agencyId, 'terminated')
        ];
    }

    /**
     * Get document statistics
     */
    private function getDocumentStats()
    {
        return [
            'total' => $this->employeeDocumentModel->countAgencyDocuments($this->agencyId),
            'pending' => $this->employeeDocumentModel->countAgencyDocumentsByStatus($this->agencyId, 'pending'),
            'verified' => $this->employeeDocumentModel->countAgencyDocumentsByStatus($this->agencyId, 'verified'),
            'rejected' => $this->employeeDocumentModel->countAgencyDocumentsByStatus($this->agencyId, 'rejected'),
            'expiring' => $this->employeeDocumentModel->countExpiringDocuments($this->agencyId, 30)
        ];
    }

    /**
     * Get banking statistics
     */
    private function getBankingStats()
    {
        return [
            'total_employees' => $this->employeeModel->countAgencyEmployees($this->agencyId),
            'banking_completed' => $this->employeeBankingModel->countCompletedBanking($this->agencyId),
            'banking_verified' => $this->employeeBankingModel->countVerifiedBanking($this->agencyId),
            'nasfund_verified' => $this->employeeBankingModel->countVerifiedNasfund($this->agencyId)
        ];
    }

    /**
     * Get qualification statistics
     */
    private function getQualificationStats()
    {
        return [
            'total' => $this->employeeQualificationModel->countAgencyQualifications($this->agencyId),
            'verified' => $this->employeeQualificationModel->countVerifiedQualifications($this->agencyId),
            'pending' => $this->employeeQualificationModel->countPendingQualifications($this->agencyId)
        ];
    }

    /**
     * Get onboarding completion data
     */
    private function getOnboardingCompletionData()
    {
        $employees = $this->employeeModel->getAgencyEmployees($this->agencyId);
        $onboardingData = [];

        foreach ($employees as $employee) {
            $completion = $this->calculateOnboardingCompletion($employee['id']);
            $onboardingData[] = [
                'employee' => $employee,
                'completion' => $completion
            ];
        }

        return $onboardingData;
    }

    /**
     * Get banking completion data
     */
    private function getBankingCompletionData()
    {
        $employees = $this->employeeModel->getAgencyEmployees($this->agencyId);
        $bankingData = [];

        foreach ($employees as $employee) {
            $banking = $this->employeeBankingModel->getEmployeeBanking($employee['id']);
            $bankingData[] = [
                'employee' => $employee,
                'banking' => $banking,
                'completion' => $this->calculateBankingCompletion($banking)
            ];
        }

        return $bankingData;
    }

    /**
     * Calculate onboarding completion percentage
     */
    private function calculateOnboardingCompletion($employeeId)
    {
        $totalItems = 5; // Basic info, documents, banking, qualifications, profile submission
        $completedItems = 0;

        // Check basic employee information (always completed if employee exists)
        $completedItems++;

        // Check documents
        $documentCount = $this->employeeDocumentModel->countEmployeeDocuments($employeeId);
        if ($documentCount > 0) {
            $completedItems++;
        }

        // Check banking information
        $banking = $this->employeeBankingModel->getEmployeeBanking($employeeId);
        if ($banking && !empty($banking['account_number'])) {
            $completedItems++;
        }

        // Check qualifications
        $qualificationCount = $this->employeeQualificationModel->countEmployeeQualifications($employeeId);
        if ($qualificationCount > 0) {
            $completedItems++;
        }

        // Check profile submission (if profile link exists and is submitted)
        $profileLink = $this->employeeProfileLinkModel->getEmployeeLink($employeeId);
        if ($profileLink && $profileLink['profile_submitted']) {
            $completedItems++;
        }

        return [
            'completed_items' => $completedItems,
            'total_items' => $totalItems,
            'percentage' => round(($completedItems / $totalItems) * 100)
        ];
    }

    /**
     * Calculate banking completion percentage
     */
    private function calculateBankingCompletion($banking)
    {
        if (!$banking) {
            return ['percentage' => 0, 'completed_fields' => 0, 'total_fields' => 6];
        }

        $totalFields = 6; // bank_name, account_name, account_number, nasfund_number, tax_file_number, account_type
        $completedFields = 0;

        if (!empty($banking['bank_name'])) $completedFields++;
        if (!empty($banking['account_name'])) $completedFields++;
        if (!empty($banking['account_number'])) $completedFields++;
        if (!empty($banking['nasfund_number'])) $completedFields++;
        if (!empty($banking['tax_file_number'])) $completedFields++;
        if (!empty($banking['account_type'])) $completedFields++;

        return [
            'completed_fields' => $completedFields,
            'total_fields' => $totalFields,
            'percentage' => round(($completedFields / $totalFields) * 100)
        ];
    }

    /**
     * Get document types
     */
    private function getDocumentTypes()
    {
        return [
            'passport' => 'Passport',
            'drivers_license' => 'Driver\'s License',
            'birth_certificate' => 'Birth Certificate',
            'academic_certificate' => 'Academic Certificate',
            'professional_license' => 'Professional License',
            'medical_certificate' => 'Medical Certificate',
            'police_clearance' => 'Police Clearance',
            'reference_letter' => 'Reference Letter',
            'other' => 'Other'
        ];
    }
}
