<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

/*
 * --------------------------------------------------------------------
 * Dakoii Portal Routes
 * --------------------------------------------------------------------
 */

// Dakoii Authentication Routes (No Auth Required)
$routes->group('dakoii', function($routes) {
    // Redirect root to login
    $routes->get('/', 'Dakoii\AuthController::login');

    // Authentication routes
    $routes->get('login', 'Da<PERSON><PERSON>\AuthController::login');
    $routes->post('authenticate', 'Da<PERSON>ii\AuthController::authenticate');
    $routes->post('logout', '<PERSON><PERSON><PERSON>\AuthController::logout');

    // Password reset routes
    $routes->get('forgot-password', 'Dakoii\AuthController::forgotPassword');
    $routes->post('send-reset-link', 'Dakoii\AuthController::sendResetLink');
});

// Dakoii Protected Routes (Auth Required)
$routes->group('dakoii', ['filter' => 'dakoii_auth'], function($routes) {
    // Dashboard
    $routes->get('dashboard', 'Dakoii\DashboardController::index');

    // Admin Users Management (Future implementation)
    $routes->group('admin-users', function($routes) {
        $routes->get('/', 'Dakoii\AdminUsersController::index');
        $routes->get('create', 'Dakoii\AdminUsersController::create');
        $routes->post('store', 'Dakoii\AdminUsersController::store');
        $routes->get('(:num)', 'Dakoii\AdminUsersController::show/$1');
        $routes->get('(:num)/edit', 'Dakoii\AdminUsersController::edit/$1');
        $routes->post('(:num)/update', 'Dakoii\AdminUsersController::update/$1');
        $routes->post('(:num)/delete', 'Dakoii\AdminUsersController::delete/$1');
    });

    // Agencies Management
    $routes->group('agencies', function($routes) {
        $routes->get('/', 'Dakoii\DakoiiAgenciesController::index');
        $routes->get('create', 'Dakoii\DakoiiAgenciesController::create');
        $routes->post('store', 'Dakoii\DakoiiAgenciesController::store');
        $routes->get('(:num)', 'Dakoii\DakoiiAgenciesController::show/$1');
        $routes->get('(:num)/edit', 'Dakoii\DakoiiAgenciesController::edit/$1');
        $routes->post('(:num)/update', 'Dakoii\DakoiiAgenciesController::update/$1');
        $routes->post('(:num)/delete', 'Dakoii\DakoiiAgenciesController::delete/$1');
    });

    // System Administration (Future implementation)
    $routes->group('system', function($routes) {
        $routes->get('audit-logs', 'Dakoii\SystemController::auditLogs');
        $routes->get('settings', 'Dakoii\SystemController::settings');
        $routes->post('settings/update', 'Dakoii\SystemController::updateSettings');
    });

    // Reports (Future implementation)
    $routes->get('reports', 'Dakoii\ReportsController::index');

    // Profile Management (Future implementation)
    $routes->get('profile', 'Dakoii\ProfileController::index');
    $routes->post('profile/update', 'Dakoii\ProfileController::update');
});

/*
 * --------------------------------------------------------------------
 * Admin Portal Routes
 * --------------------------------------------------------------------
 */

// Admin Authentication Routes (No Auth Required)
$routes->group('admin', function($routes) {
    // Redirect root to login
    $routes->get('/', 'Admin\AuthController::login');

    // Authentication routes
    $routes->get('login', 'Admin\AuthController::login');
    $routes->post('authenticate', 'Admin\AuthController::authenticate');
    $routes->post('logout', 'Admin\AuthController::logout');

    // Password reset routes
    $routes->get('forgot-password', 'Admin\AuthController::forgotPassword');
    $routes->post('send-reset-link', 'Admin\AuthController::sendResetLink');
});

// Admin Protected Routes (Auth Required)
$routes->group('admin', ['filter' => 'admin_auth'], function($routes) {
    // Dashboard
    $routes->get('dashboard', 'Admin\DashboardController::index');

    // User Management (Future implementation)
    $routes->group('users', function($routes) {
        $routes->get('/', 'Admin\UsersController::index');
        $routes->get('create', 'Admin\UsersController::create');
        $routes->post('store', 'Admin\UsersController::store');
        $routes->get('(:num)', 'Admin\UsersController::show/$1');
        $routes->get('(:num)/edit', 'Admin\UsersController::edit/$1');
        $routes->post('(:num)/update', 'Admin\UsersController::update/$1');
        $routes->post('(:num)/delete', 'Admin\UsersController::delete/$1');
    });

    // Agency Users Management (Future implementation)
    $routes->group('agency-users', function($routes) {
        $routes->get('/', 'Admin\AgencyUsersController::index');
        $routes->get('assign', 'Admin\AgencyUsersController::assign');
        $routes->post('assign/store', 'Admin\AgencyUsersController::storeAssignment');
        $routes->post('(:num)/unassign', 'Admin\AgencyUsersController::unassign/$1');
    });

    // Reports (Future implementation)
    $routes->get('reports', 'Admin\ReportsController::index');

    // Settings (Future implementation)
    $routes->get('settings', 'Admin\SettingsController::index');
    $routes->post('settings/update', 'Admin\SettingsController::update');

    // Profile Management (Future implementation)
    $routes->get('profile', 'Admin\ProfileController::index');
    $routes->post('profile/update', 'Admin\ProfileController::update');
});

/*
 * --------------------------------------------------------------------
 * Agency Portal Routes
 * --------------------------------------------------------------------
 */

// Agency Protected Routes (Auth Required) - Temporarily disabled for testing
$routes->group('agency', function($routes) {
    // Dashboard
    $routes->get('/', 'Agency\AgencyDashboardController::index');
    $routes->get('dashboard', 'Agency\AgencyDashboardController::index');
    $routes->get('dashboard/stats', 'Agency\AgencyDashboardController::getStats');
    $routes->get('dashboard/activity', 'Agency\AgencyDashboardController::getRecentActivity');

    // Employee Management - REMOVED

    // Onboarding Management - REMOVED

    // Document Management - REMOVED (Controller deleted)

    // Reports
    $routes->group('reports', function($routes) {
        $routes->get('/', 'Agency\AgencyReportsController::index');
        // $routes->get('employees', 'Agency\AgencyReportsController::employees'); // REMOVED
        // $routes->get('onboarding', 'Agency\AgencyReportsController::onboarding'); // REMOVED
        // $routes->get('documents', 'Agency\AgencyReportsController::documents'); // REMOVED
        // $routes->get('banking', 'Agency\AgencyReportsController::banking'); // REMOVED
    });

    // Profile Management
    $routes->get('profile', 'Agency\AgencyProfileController::index');
    $routes->post('profile/update', 'Agency\AgencyProfileController::update');
});

/*
 * --------------------------------------------------------------------
 * Employee Self-Service Routes - REMOVED
 * --------------------------------------------------------------------
 */
